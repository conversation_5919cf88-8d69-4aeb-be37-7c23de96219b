<script setup>
  import {defineEmits, ref} from 'vue';

  const emit = defineEmits(['add-new-item']);
  const props = defineProps({
    resourceType: Number,
    languageCode: String,
  });

  const itemData = ref({
    name: '',
    column_name: '',
    input_method: 1,
    is_required: 0,
    input_validation: 1,
    language_code: '',
  });
  const resourceList = [
    {value: 1, label: '商品'},
    {value: 2, label: '会員'},
  ];
  const itemNameList = [
    {value: 1, label: 'メーカー'},
    {value: 2, label: 'モデル'},
    {value: 3, label: '色'},
    {value: 4, label: 'サイズ'},
    {value: 5, label: '価格'},
    {value: 6, label: '在庫'},
  ];

  const addNewItem = () => {
    console.log('addNewItem');
    emit('add-new-item', {
      ...itemData.value,
      language_code: props.languageCode,
      resource_type: props.resourceType,
    });
  };
</script>

<template>
  <div>
    <div>
      <CForm onsubmit="return false;">
        <CRow class="mb-3">
          <CCol sm="3">
            <label>リソース</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="resource_type"
              :options="resourceList"
              v-model="itemData.resource_type"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>項目名</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="item_name"
              :options="itemNameList"
              v-model="itemData.item_name"
            />
          </CCol>
        </CRow>
      </CForm>
    </div>
    <div class="mt-4">
      <CRow>
        <CCol sm="2" class="d-grid">
          <CButton size="sm" color="primary" @click="addNewItem">追加</CButton>
        </CCol>
      </CRow>
    </div>
  </div>
</template>
