<script setup>
import { defineAsyncComponent } from 'vue'
import { useMessageDialogStore } from '../../stores/messag-dialog'
const dialog = useMessageDialogStore()
const ListCard = defineAsyncComponent(
  () => import(/* webpackChunkName: "ListCard" */ './ListPageComponent.vue')
)
defineProps(['product'])
</script>
<template>
  <div class="modal-container" :class="dialog.show ? 'active' : ''">
    <div class="modal-body">
      <div class="modal-close" @click="dialog.show = false">×</div>
      <div class="modal-content">
        <ul class="matching-dir" style="margin-bottom: 0">
          <Suspense>
            <ListCard :product="product" />
          </Suspense>
        </ul>
        <div class="btn-form">
          <input type="button" id="sbm-login" value="入力内容を確認する" disabled="disabled" />
        </div>
      </div>
    </div>
  </div>
</template>
