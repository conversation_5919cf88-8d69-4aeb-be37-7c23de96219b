<script setup>
import { defineAsyncComponent, onBeforeMount, watch } from 'vue'
import { useLocale } from 'vuetify'
import useSearchProducts from '../../composables/searchProducts'
import useApi from '../../composables/useApi'
import { useMessageDialogStore } from '../../stores/messag-dialog'

const NoticeList = defineAsyncComponent(() => import('./NoticeList.vue'))
const NewsHeadline = defineAsyncComponent(() => import('./NewsHeadline.vue'))
const FilterBox = defineAsyncComponent(() => import('../search-list/parts/FilterBox.vue'))
const TopPageExhibitions = defineAsyncComponent({
  loader: () => import('./TopPageExhibitions.vue')
})

const { t, current } = useLocale()
const { parseHtmlResponseError } = useApi()
const dialog = useMessageDialogStore()
const { getConstants, search, resetParams } = useSearchProducts()

const getData = async () => {
  try {
    await Promise.all([getConstants(), search({ initLimit: 20 })])
  } catch (error) {
    const err = parseHtmlResponseError(error)
    console.log({ err })
    dialog.setShowMessage(err.message ?? t('common.error'), { isErr: true })
  }
}
onBeforeMount(async () => {
  resetParams()
  await getData()
})

watch(
  () => current.value,
  async () => {
    await getData()
  }
)
</script>
<template>
  <div>
    <NewsHeadline />
    <FilterBox />
    <TopPageExhibitions />
    <NoticeList />
  </div>
</template>
