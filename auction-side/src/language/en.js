import { en as enLocale } from 'vuetify/locale'

export const en = {
  $vuetify: {
    ...enLocale,
    dataIterator: {
      rowsPerPageText: 'Number of items displayed:',
      pageText: '{0}-{1} of {2}'
    }
  },
  common: {
    back: 'Back',
    backList: 'Back to List',
    more: 'Show More',
    japan: 'Japan',
    dubai: 'Dubai',
    hongkong: 'Hong Kong',
    mitaiou: 'Unset',
    send: 'Send',
    agree: 'Agree',
    error: 'Error occurred.',
    confirm: 'Confirm',
    inputError: 'Input Error',
    dateFormat: 'yyyy/MM/dd',
    remove: 'Remove',
    updateAuction: 'Update',
    day: 'd',
    hour: 'h',
    minute: 'm',
    second: 's'
  },
  siteTitle: 'WORLDMOBILE Auction',
  copyright: 'Copyright ©2011-WORLDMOBILE All Rights Reserved.',

  top: {
    appBar: {
      selectCategory: 'Select Category',
      register: 'New Member Registration',
      login: 'Sign In',
      logout: 'Sign Out'
    }
  },

  productDetail: {
    title: 'Product Details',
    currency: '$',
    quantity: 'Quantity',
    lowestBidQuantity: 'Minimum bid quantity',
    lowestBidPrice: 'Minimum Bid Unit Price',
    bidCount: 'Number of bids',
    bidQuantity: 'Bid Quantity',
    bidUnitPrice: 'Bid Unit Price', // 入札単価
    bidPriceForAscAuction: 'Bid Price', // 入札価格
    bidTotalPrice: 'Total Bid Amount',
    bidButton: 'Place Bid',
    contactButton: 'Inquiries about this product',
    aboutRank: 'Product Grade Details',

    info: {
      maker: 'Manufacturer',
      productName: 'Product Name',
      sim: 'SIM',
      capacity: 'Capacity',
      color: 'Color',
      rank: 'Grade',
      quantity: 'Quantity',
      note1: 'Note 1',
      note2: 'Note 2',
      lowestBidPrice: 'Minimum Bid Price',
      lowestBidQuantity: 'Minimum Bid Quantity',
      favorite: 'Favorite',
      startPrice: 'Starting Price',
      currentPrice: 'Current Price'
    },
    bidModal: {
      allExhTotalPrice: 'Total Bid Price',
      exhTotalPrice: 'Auction Total Price',
      terms: 'Terms and Conditions',
      termsAgree: 'I agree to the Terms of Conditions',
      noteBid: 'Click "Place Bid" if the information is correct.',
      bidButton: 'Place Bid',
      loginRequiredMessage: 'Bidding is available to members only. Please sign in.',
      bidSuccessFulMessage: 'Bids have been accepted.',
      closeButton: 'Close'
    },
    errorMessages: {
      priceInvalidFormat: 'Bid prices must be entered as numbers only.',
      priceMaxLength: 'Please enter the bid price in {0} digits or less.',
      lowestUnitPriceInvalid:
        'Please enter a number greater than or equal to the minimum bid unit price.',
      lowestBidPriceInvalid: 'Please enter a value greater than the start price.', // use for AscendingAution
      quantityInvalidFormat: 'Bid quantity must be entered as a number only.',
      quantityMaxLength:
        'Bidding quantity must be entered as a number less than or equal to {0} digits.',
      lowestBidQuantityInvalid:
        'Please enter a number greater than or equal to the minimum bid quantity.',
      bidQuantityExceedsMax: 'Bidding quantity exceeds the number of items on display.',
      priceEmpty: 'Please enter a bid price.',
      quantityEmpty: 'Please enter the bid quantity.',
      bidSuccess: 'Bid Success',
      bidFailed: 'Bid Failed',
      newPriceLowerThanCurrentPriceErr: 'Cannot place a bid below the current price.'
    }
  },
  bulkBid: {
    bulkBidButton: 'Bulk Bid',
    bulkReBidButton: 'Bulk Rebid',
    noBid: 'Please enter quantity and unit price.',
    inputBidPrice: 'Bidding Unit Price:',
    inputBidQuantity: 'Bidding Quantity:',
    inputErrorMessage: 'There is an error in your input. Please correct it before proceeding.',
    note: 'You can place batch bids for items for which the Bid Quantity and Bid Unit Price have values. You can also place bids on multiple auctions at once.'
  },
  register: {
    title: 'Member Registration',
    titleConfirm: 'Membership Registration Confirmation',
    subtitle:
      'Once you have prepared the necessary documents, please fill in the form below. <br />Fields marked <em class="req">Required</em> must be completed.',
    subtitleConfirm: 'Please review your entries and click "Submit" if everything is correct.',

    form: {
      customerCode: 'Customer Code',
      country: 'Country',
      ceoName: "Representative's Full Name",
      ceoNameKana: "Representative's Name",
      ceoBirthday: "Representative's Date of Birth",
      companyName: 'Company Name',
      companyNameKana: 'Company Name',
      companyAddress: 'Company Address',
      establishmentDate: 'Company Establishment Date',
      companyHp: 'Company Website',
      businessContent: 'Business Description',
      invoiceNo: 'Invoice Number',
      telCountryCode: 'Country Code',
      tel: 'Phone Number',
      antiquePermitNo: 'Secondhand dealer license<br class="only_pc">License Number',
      antiquePermitDate: 'Secondhand dealer license<br class="only_pc">Issue Date',
      antiquePermitCommission:
        'Secondhand dealer license<br class="only_pc">Public Safety Commission',
      memberLastName: 'Contact Person Last Name',
      memberName: 'Contact Person Full Name',
      whatsApp: 'WhatsApp',
      wechat: 'WeChat',
      memo: 'Notes',
      email: 'Email Address',
      emailLang: 'Email language',
      emailConfirm: 'Confirm Email Address',
      currentPassword: 'Current Password',
      password: 'Password',
      passwordConfirm: 'Confirm Password',
      ruleCheck: 'Privacy Policy<br class="only_pc">Agreement',

      passwordHint: '※Password must be 8-14 characters and include both letters and numbers.',

      fullWidth: '[Full-width]',
      halfWidth: '[Half-width]',
      halfWidthNum: '[Half-width Numbers]',
      halfWidthEnglish: '[half-width alphanumeric character]',
      kana: '[Katakana]',

      emailCompareError: 'Email addresses do not match.',
      passwordCompareError: 'Passwords do not match.',
      required: 'Required',
      privacyMessage1: 'Please review the ',
      privacyMessage2: 'Privacy Policy',
      privacyMessage3: ' before sending.',
      agree: 'Agree',
      confirmButton: 'Confirm',
      withdraw: 'Membership cancel',
      withdrawConfirm: 'May I cancel my membership?',
      withdrawFinish: 'Membership cancellation completed.',
      changeFinish: 'Member information editing completed.',
      registerFinish: 'Member application completed.',
      countryNoSelect: '',
      kataKanaError: 'Please enter in katakana.'
    },
    datePicker: {
      dateFormat: 'yyyy/MM/dd',
      year: 'Year',
      month: 'Month',
      day: 'Day'
    },

    firstLastName: {
      lastName: 'Last Name',
      firstName: 'First Name'
    }
  },
  contact: {
    entryFormInfo1: 'Please enter the following items.',
    entryFormInfo2: 'items must be completed.',
    confirmMsg: 'Please review your entries and click "Submit" if everything is correct.',
    companyName: 'Company Name',
    memberName: 'Contact Person',
    mailConfirm: '(Confirmation)',
    inquiryContent: 'Inquiry Content',
    halfWidthEnglish: 'half-width alphanumeric character',
    policyMessage1: 'Handling of',
    policyMessage2: 'Personal Information',
    inquiryDoneMessage: 'Your inquiry has been completed.'
  },
  favorite: {
    title: 'Favorite',
    empty: 'Your favorites list is currently empty.',
    clearPriceInput: 'Clear Input',
    subTotalBidPriceHeader: 'Bidding<br>Subtotal',
    subTotalBidPrice: 'Bidding Subtotal',
    loginRequiredFavorite: 'You can put the item in your favorites list after signing in.',
    bidButton: 'Place a bid',
    reBidButton: 'Rebid',
    bidQuantity: 'Bid Quantity',
    deleteFavorite1: 'Remove from',
    deleteFavorite2: 'Favorites'
  },
  auction: {
    bidOngoing: 'Bidding',
    bidHistory: 'Bid History',
    successResult: 'Auction Results'
  },
  bidHistory: {
    endDate: 'End Date',
    bidSuccessUnitPrice: 'Winning Price',
    bidSuccessPrice: 'Winning Price',
    bidSuccessQuantity: 'Winning Qty',
    bidTotalPrice: 'Total Amount'
  },
  guidance: {
    auction: 'Auction',
    auctionOngoing: 'Current Auctions',
    auctionResult: 'Auction Results',
    aboutMembership: 'About Members',
    register: 'New Member Registration',
    login: 'Sign in',
    firstTime: 'For First Time Users',
    information: 'Inquiry',
    contact: 'Contact us',
    aboutUs: 'About us',
    companyOverview: 'Company Profile',
    terms: 'Terms of Service',
    toshuho: 'Specified Commercial Transactions Law',
    privacy: 'Privacy Policy',
    guide: "Auction User's Guide",
    companyInfo: 'Company Information',
    cookieSetting: 'Cookie Settings'
  },
  user: {
    myPage: 'My Page',
    editProfile: 'Edit Profile'
  },
  filterBox: {
    title: 'Advanced Search',
    inputPlaceholder: 'Enter manufacturer or product name',
    keyword: 'Keyword',
    searchButton: 'Search with these conditions',
    category: 'Category',
    clearConditions: 'Clear the conditions',
    auctionCount: 'auctions',
    searchCriteria: 'Search condition'
  },
  notice: {
    noticeLabel: 'Notice',
    informationLabel: 'Information',
    more: 'View More',
    noticeList: 'List of Notices',
    importantNotice: 'Important Notice',
    importantNoticeList: 'Important Notice List',
    attachedFile: 'Attached File'
  },
  auth: {
    logoutMessage: 'Would you like to sign out?',
    logout: 'Sign out',
    close: 'Close',
    cancel: 'Cancel'
  },
  login: {
    title: 'Sign in',
    email: 'Email Address',
    password: 'Password',
    saveLoginInfo: 'Save email address and password',
    forgetPassword: 'Forgot Password?',
    rule: 'Terms of Service',
    agreeRule: 'I agree to the Terms of Service',
    agree: 'Agree',
    entryInfo1: 'New Member Registration (Free)',
    entryInfo2: 'Registration is required to bid on items.',
    confirmButton: 'Sign in',
    passwordHint: '8-16 alphanumeric characters',
    emailConfirm: 'Email address (for confirmation)',
    reminderForgetPassword: 'Forgot your password?',
    sendButton: 'Submit',
    reminderMessage1:
      'If you have forgotten your password, please enter your registered e-mail address.',
    reminderMessage2:
      'When you click the “Send” button, your password will be sent to your registered e-mail address.',
    reminderEmailCheckError: 'Please confirm your e-mail address.',
    reminderConfirmEmailCheckError: 'Please confirm your e-mail address (for confirmation).',
    reminderCompleteMessage: 'A new password has been sent to your e-mail address.',
    reminderCompleteButton: 'Return to Sign In Screen',

    reSettingPasswordAtFirstTime: 'Reset Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    newPasswordConfirm: 'New Password (Confirm)'
  },

  route: {
    top: 'TOP',
    login: 'Sign In',
    reminder: 'Forgot Password',
    register: 'Membership Request',
    noticeList: 'Notice List',
    importantNoticeList: 'Important Notices',
    importantNotice: 'Important Notice',
    bidHistoryAll: 'Auction Results',
    details: 'Product Details',
    contact: 'Inquiry',
    contactConfirm: 'Inquiry Confirmation',
    noticeDetails: 'Notification',
    favorites: 'My favorites',
    bidOngoing: 'Bidding',
    bidHistory: 'Bid History',
    myPage: 'My Page',
    myPageEditConfirm: 'Member Edit Confirmation',
    companyOverview: 'Company Profile',
    terms: 'Terms of Service',
    privacy: 'Privacy Policy',
    firstTime: 'For First Time Users',
    toshuho: 'Specified Commercial Transactions Law'
  },
  COMMON_BID_LABEL: 'Bid',
  BID_COUNT: '', // exg: 入札数: 2件(JA), Bid count: 2 (EN)
  CLASSIFICATION_ASCENDING: 'Ascending Auction',
  CLASSIFICATION_SEALED: 'Sealed Auction',
  ASCENDING: 'Ascending',
  SEALED: 'Sealed',
  BID_STATUS_INPROGRESS: 'In Progress',
  BID_STATUS_CANCEL: 'Suspension of exhibits',
  BID_STATUS_NOT_START_YET: 'Waiting to Start',
  BID_STATUS_EXTENDING: 'Extending',
  BID_STATUS_ENDED: 'End',
  HIGHEST_BIDDER: 'Highest Bidder',
  BID_STATUS: 'Status',
  REMAINING_TIME: 'Remaining time',
  ANATA_TOP: 'Top bidder',
  RESERVE_PRICE_NOT_MET: 'The minimum bid price has not been reached.',
  RESERVE_PRICE_EXCEEDED: 'Exceeding the minimum required bid',
  MORE_LITTLE: 'Just A little more',
  SECOND_BIDDER: '2nd place bidder',
  END_DATE_TIME: 'End date and time',
  START_DATE_TIME: 'Start date and time',
  RECORDED_BID_PRICE: 'Your Price:'
}
