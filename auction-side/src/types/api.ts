// API related type definitions

import type { AxiosError } from 'axios'

// Generic API response structure
export interface ApiResponse<T = any> {
  data?: T
  message?: string
  errors?: any
  error?: string
  rawResponse?: string
}

// Login specific response
export interface LoginResponse {
  token: string
  user_name: string
  language?: string
  next_step: string
}

// API error response structure
export interface ApiErrorResponse {
  message?: string
  errors?: any
  error?: string
  rawResponse?: string
}

// API request parameters
export interface ApiRequestParams {
  [key: string]: any
  languageCode?: string
}

// API headers
export interface ApiHeaders {
  [key: string]: string | undefined
  authorization?: string
  'Content-Type'?: string
}

// API configuration
export interface ApiConfig {
  headers?: ApiHeaders
}

// useApi composable return type
export interface UseApiReturn {
  apiExcute: <T = any>(
    path: string,
    params?: ApiRequestParams,
    fileUpload?: boolean,
    headers?: ApiHeaders
  ) => Promise<T>
  parseHtmlResponseError: (error: AxiosError | any) => any
}

// Buffer response type for compressed data
export interface BufferResponse {
  type: 'Buffer'
  data: number[]
}
