<script setup>
import { computed, defineAsyncComponent, onBeforeMount, onMounted, ref, watch } from 'vue'
import { RouterLink, useRoute, useRouter } from 'vue-router'
import { PATH_NAME } from '../defined/const'

import useBid from '../composables/bid'
import { pdfViewerUrl, priceLocaleString } from '../composables/common'
import useFavorite from '../composables/favorite'
import useGetItemDetails from '../composables/getItemDetails'

import { useLocale } from 'vuetify'
import ProductInfo from '../components/detail/ProductInfo.vue'
import ExtendCountDown from '../components/parts/ExtendCountDown.vue'
import useApi from '../composables/useApi'
import useWebSocket from '../composables/websocket'
import { useAuthStore } from '../stores/auth'
import { useBidConfirmStore } from '../stores/bidConfirm'
import { useItemDetailStore } from '../stores/item-detail'
import { useMemberStore } from '../stores/member-info'
import { usePrevRouteStore } from '../stores/prev-route'
import { useSearchResultStore } from '../stores/search-results'
import { getAuctionStatusLabel, isPC } from '../utils'

const BidConfirmModal = defineAsyncComponent(
  () => import('../components/detail/BidConfirmModal.vue')
)

const { t, current } = useLocale()
const route = useRoute()
const router = useRouter()
const { apiExcute } = useApi()
const auth = useAuthStore()
const { prevRoute, getPrevRoute } = usePrevRouteStore()
const store = useSearchResultStore()
const member = useMemberStore()
const bid = useBidConfirmStore()
const { toggleFavorite } = useFavorite()
const { search: findProductByID, getConstants } = useGetItemDetails()
const { constants } = useItemDetailStore()
const { priceMaxLength, bidQuantity, bidPrice, bidTotalPrice, bidHandle } = useBid()

// WebSocket処理
const { connectWs } = useWebSocket(store, route)

const isFavoritedLocal = ref(false)
const isManageNo = ref('')
const isAuctionPreviewEnd = ref(true)
const isLoading = ref(true)

const prevRouteValue = computed(() => prevRoute)
const detail = computed(() => store.productDetails)
const canBid = computed(
  () => detail.value?.bid_status?.can_bid === true && !detail.value?.bid_status?.sold_out
)
const isAscendingAuction = computed(() => Number(detail.value?.auction_classification) === 1)
const hasUserBid = computed(() => Number(detail.value?.bid_count) >= 1)

const statusLabel = getAuctionStatusLabel(detail, t, 'detail')

const rankPdfUrlAvailable = computed(() => {
  const pdf = constants.find(
    (x) => x.key_string === 'PRODUCT_RANK_PDF' && x.value1 === detail.value.freeFields?.rank
  )
  return !!pdf?.file_url
})

const handleFavorite = () => {
  toggleFavorite(detail.value.exhibition_item_no, isFavoritedLocal.value)
  if (auth.isAuthenticated) {
    isFavoritedLocal.value = !isFavoritedLocal.value
  }
}

// 更新・取得処理
const refreshItem = async () => {
  await findProductByID(route.params.manageNo ?? '')
  console.log('🪠refreshItem: ', detail.value)
  if (!canBid.value) {
    bidPrice.value = detail.value?.bid_status?.bid_price || ''
  }
}

// 初期表示処理
const getDetailData = async () => {
  isLoading.value = true
  isManageNo.value = route.params.manageNo

  // ManageNoがない場合は商品一覧に遷移
  if (!isManageNo.value) {
    router.push(PATH_NAME.TOP)
    return
  }

  // delete makeshop logic???
  const isFromMakeshop = !!route.query?.brand_code
  const responses = await Promise.all([
    auth.isAuthenticated ? apiExcute('private/get-change-info-constants') : null,
    getConstants(),
    await findProductByID(isManageNo.value ?? '', isFromMakeshop ? 'makeshop' : null)
  ])
  if (store.productDetails?.exhibition_item_no) {
    // Makeshopからアクセスがあった場合、該当の商品コードでオークション中の商品が無い場合は商品一覧に遷移させる
    if (isFromMakeshop && !store.productDetails.bid_status.can_bid) {
      router.push(PATH_NAME.TOP)
      return
    }
    store.setProductListForContact()
    if (auth.isAuthenticated) {
      member.setMemberInfo(responses[0])
    }
    // Initiate bid price when page is loaded
    if (detail.value.bid_status?.bid_price && detail.value.bid_status.pitch_width) {
      bidPrice.value = priceLocaleString(detail.value.bid_status.bid_price)
      // 競り上げの場合は「bidQuantity = ’1’」
      bidQuantity.value = priceLocaleString(
        detail.value.auction_classification === 1 ? '1' : detail.value.bid_status.bid_quantity
      )
    }
  } else {
    router.push(PATH_NAME.TOP)
  }
  isLoading.value = false
}

// チャットボタンクリック時の処理
const handleContact = () => {
  router.push(`${PATH_NAME.DETAIL}/${detail.value.exhibition_item_no}/contact`)
}

const handleRankPdf = () => {
  const pdf = constants.find(
    (x) => x.key_string === 'PRODUCT_RANK_PDF' && x.value1 === detail.value.freeFields?.rank
  )
  if (pdf && pdf.file_url) {
    window.open(pdfViewerUrl(`/${pdf.file_url}`), '_blank')
  }
}

const isPastTime = (dateTime) => {
  if (!dateTime) return false
  const timestamp = new Date(dateTime).getTime()
  if (isNaN(timestamp)) return false
  return timestamp < Date.now()
}

const handleBidButtonClick = () => {
  if (isAscendingAuction.value) {
    bidQuantity.value = 1
  }
  bidHandle({
    exhibitionNo: detail.value.exhibition_no,
    exhibitionItemNo: detail.value.exhibition_item_no,
    exhibitionName: detail.value.exhibition_name,
    lowestBidPrice: detail.value?.bid_status.lowest_bid_price,
    lowestBidQuantity: detail.value?.bid_status.lowest_bid_quantity,
    pitchWidth: detail.value?.bid_status.pitch_width,
    freeField: detail.value.freeFields,
    newBidPrice: bidPrice.value,
    newBidQuantity: bidQuantity.value,
    enteredBidPrice: detail.value?.bid_status.bid_price,
    currentBidPrice:
      detail.value?.bid_status.current_price || detail.value?.bid_status.lowest_bid_price,
    enteredBidQty: detail.value?.bid_status.bid_quantity,
    maxQuantity: detail.value?.bid_status.quantity,
    isAscendingAuction: isAscendingAuction.value,
    hasUserBid: hasUserBid.value
  })
}

onBeforeMount(async () => {
  await getDetailData()
})

onMounted(() => {
  setTimeout(() => {
    window.scrollTo({ top: 0 })
  }, 500)

  // Get isFavorited from store when initial load
  if (detail.value?.attention_info) {
    isFavoritedLocal.value = detail.value.attention_info.is_favorited
  }
  // Connect websocket
  connectWs(auth.token)
})

// Update local state when favorite is updated
watch(
  () => detail.value?.attention_info?.is_favorited,
  (newVal) => {
    isFavoritedLocal.value = newVal
  },
  { immediate: true }
)

watch(
  () => route.params.manageNo,
  async () => {
    await getDetailData()
  }
)

watch(
  () => current.value,
  async () => {
    await getDetailData()
  }
)

watch(
  () => detail.value.preview_end_datetime,
  (newVal) => {
    if (newVal) {
      const result = isPastTime(newVal)
      isAuctionPreviewEnd.value = result
    }
  }
)

onMounted(() => {
  if (isPC.value) {
    const newRoute = getPrevRoute()
    prevRouteValue.value.name = newRoute?.meta?.name
  }
})
</script>
<template>
  <div>
    <div id="pNav">
      <ul>
        <li><RouterLink :to="PATH_NAME.TOP">TOP</RouterLink></li>
        <li v-if="prevRouteValue.path && prevRouteValue.name && prevRouteValue.path !== '/'">
          <a @click="router.push(prevRouteValue.path)" class="cursor-pointer">{{
            t(prevRouteValue.name)
          }}</a>
        </li>
        <li>{{ detail?.freeFields?.product_name }}</li>
      </ul>
    </div>

    <section id="item-detail">
      <div class="container">
        <div id="item-data-no-image">
          <div class="item-name">
            <p>{{ detail?.freeFields?.product_name }}</p>
          </div>
          <div class="item_main">
            <div class="spec-field">
              <!--<h2>スペック</h2>-->
              <ProductInfo :freeField="detail?.freeFields" />
            </div>
            <div class="bid-field">
              <div class="auction-type">
                <div class="title">
                  <span class="mark">
                    {{
                      isAscendingAuction
                        ? t('CLASSIFICATION_ASCENDING')
                        : t('CLASSIFICATION_SEALED')
                    }}</span
                  >
                </div>
                <button @click="refreshItem">
                  <img src="@/assets/img/common/icn_refresh_w.svg" /><span>{{
                    t('common.updateAuction')
                  }}</span>
                </button>
              </div>

              <!-- 競り上げ -->
              <template v-if="isAscendingAuction">
                <div class="item-data">
                  <dl class="ascending">
                    <dt>{{ t('productDetail.info.startPrice') }}</dt>
                    <dd class="start-price">
                      <span class="unit">{{ t('productDetail.currency') }}</span>
                      <span>{{ priceLocaleString(detail.bid_status.lowest_bid_price) }}</span>
                    </dd>
                    <dt>
                      {{ t('productDetail.info.currentPrice') }}
                    </dt>
                    <dd class="price">
                      <span class="unit">{{ t('productDetail.currency') }}</span>
                      <span class="current-price">{{
                        priceLocaleString(detail.bid_status.current_price)
                      }}</span>
                    </dd>
                  </dl>
                  <dl class="status">
                    <dt>
                      {{ t('BID_STATUS') }}
                    </dt>
                    <dd>
                      <span class="time">
                        {{ statusLabel }}
                      </span>
                    </dd>
                  </dl>

                  <!-- 終了日時 -->
                  <dl class="hide-initial">
                    <dt>
                      {{ detail?.bid_status?.started ? t('END_DATE_TIME') : t('START_DATE_TIME') }}
                    </dt>
                    <dd>
                      <span class="">
                        <div v-if="detail?.bid_status?.started" class="end-v">
                          {{ detail?.endDatePart }} {{ detail?.endTimePart }}
                        </div>
                        <div v-else class="end-v">
                          {{ detail?.startDatePart }} {{ detail?.startTimePart }}
                        </div>
                      </span>
                    </dd>
                  </dl>

                  <!-- 残り時間 -->
                  <dl v-if="canBid && detail?.bid_status?.extending" class="countdown">
                    <dt>{{ t('REMAINING_TIME') }}</dt>
                    <dd>
                      <ExtendCountDown
                        class="d-inline-flex"
                        :remainingSeconds="detail?.bid_status?.remaining_seconds"
                        @countdown-ended="refreshItem"
                      />
                    </dd>
                  </dl>
                  <!-- 入札件数デフォルトでは非表示 -->
                  <dl v-if="detail?.attention_info?.show_bid_count_flag" class="num hide-initial">
                    <dt>{{ t('productDetail.bidCount') }}</dt>
                    <dd>
                      <span class="amount"
                        >{{ priceLocaleString(detail.bid_count ?? 0) }}{{ t('BID_COUNT') }}</span
                      >
                    </dd>
                  </dl>
                </div>
                <div
                  :class="{
                    'mt-2': !detail?.attention_info?.show_bid_count_flag,
                    'label-wrap': true
                  }"
                >
                  <p v-show="auth.isAuthenticated && detail?.bid_status?.is_top_member" class="top">
                    <span>{{ t('ANATA_TOP') }}</span>
                  </p>
                  <p
                    v-show="auth.isAuthenticated && detail?.bid_status?.is_second_member"
                    class="second"
                  >
                    <span>{{ t('SECOND_BIDDER') }}</span>
                  </p>
                  <p v-if="detail?.bid_status?.is_not_exceeding_lowest_price" class="reserve">
                    <span>{{ t('RESERVE_PRICE_NOT_MET') }}</span>
                  </p>
                  <p v-if="detail.bid_status.is_exceeding_lowest_price" class="exceeding">
                    <span>{{ t('RESERVE_PRICE_EXCEEDED') }}</span>
                  </p>
                  <p v-if="detail?.bid_status.is_more_little" class="exceeding">
                    <span>{{ t('MORE_LITTLE') }}</span>
                  </p>
                </div>
              </template>

              <!-- 封印 -->
              <template v-if="!isAscendingAuction">
                <div class="item-data">
                  <dl class="item-quantity">
                    <dt>{{ t('productDetail.quantity') }}</dt>
                    <dd>{{ priceLocaleString(detail?.bid_status?.quantity) }}</dd>
                  </dl>
                  <dl>
                    <dt>{{ t('productDetail.lowestBidQuantity') }}</dt>
                    <dd>{{ priceLocaleString(detail?.bid_status?.lowest_bid_quantity) }}</dd>
                    <dt>{{ t('productDetail.lowestBidPrice') }}</dt>
                    <dd>
                      <span class="unit">{{ t('productDetail.currency') }}</span
                      >{{ priceLocaleString(detail?.bid_status?.lowest_bid_price) }}
                    </dd>
                  </dl>

                  <!-- 入札件数デフォルトでは非表示 -->
                  <dl v-if="detail?.attention_info?.show_bid_count_flag" class="num">
                    <dt>{{ t('productDetail.bidCount') }}</dt>
                    <dd>{{ priceLocaleString(detail?.bid_count || 0) }}</dd>
                  </dl>
                </div>
              </template>

              <div class="place-bid">
                <!-- 入札数量 -->
                <div v-show="!isAscendingAuction && !isLoading" class="bid-amount">
                  <div class="bid_head">
                    <p class="ttl">
                      <span class="txt-error-w">{{ t('common.inputError') }}</span>
                      <span>{{ t('productDetail.bidQuantity') }}</span>
                    </p>
                    <p class="input-field">
                      <input
                        v-model="bidQuantity"
                        :class="{
                          'price-bid': true,
                          border: true,
                          'bg-white': canBid,
                          'bg-surface-light': !canBid
                        }"
                        placeholder="10"
                        :disabled="!canBid"
                        @input="
                          bidQuantity = bidQuantity
                            ?.replace(/[^0-9]/g, '')
                            ?.slice(0, priceMaxLength)
                        "
                        @blur="
                          () => {
                            bidQuantity = priceLocaleString(bidQuantity)
                          }
                        "
                      />
                    </p>
                  </div>
                </div>
                <!-- 入札単価（封印）・入札価格（競り上げ） -->
                <div class="bid-price">
                  <div class="bid_head">
                    <p class="ttl">
                      <span class="txt-error-w">{{ t('common.inputError') }}</span>
                      <span>{{
                        isAscendingAuction
                          ? t('productDetail.bidPriceForAscAuction')
                          : t('productDetail.bidUnitPrice')
                      }}</span>
                    </p>
                    <p class="input-field">
                      <span class="unit">{{ t('productDetail.currency') }}</span>
                      <input
                        v-model="bidPrice"
                        :class="{
                          'price-bid': true,
                          border: true,
                          'bg-white': canBid,
                          'bg-surface-light': !canBid
                        }"
                        placeholder="10"
                        :disabled="!canBid"
                        @input="
                          bidPrice = bidPrice?.replace(/[^0-9]/g, '')?.slice(0, priceMaxLength)
                        "
                        @blur="
                          () => {
                            bidPrice = priceLocaleString(bidPrice)
                          }
                        "
                      />
                    </p>
                  </div>
                </div>
                <!-- 入札合計金額 -->
                <div v-show="!isAscendingAuction" class="total-bid-price">
                  <p class="price-label">{{ t('productDetail.bidTotalPrice') }}</p>
                  <p class="total-bid-amount">
                    <span class="unit">{{ t('productDetail.currency') }}</span
                    >{{ bidTotalPrice }}
                  </p>
                </div>
              </div>

              <div class="btn-bid-wrap">
                <div class="button-bid">
                  <v-btn
                    class="btn bid modal-open"
                    :disabled="!canBid || !bidPrice"
                    @click="handleBidButtonClick"
                    >{{ t('productDetail.bidButton') }}</v-btn
                  >
                  <button v-show="!isAuctionPreviewEnd" class="fav-mark" @click="handleFavorite">
                    <p class="fav-pct" v-bind:class="{ added: isFavoritedLocal }"></p>
                  </button>
                </div>
              </div>
              <div class="btn-wrap-contact">
                <a @click="handleContact()" class="contact cursor-pointer">{{
                  t('productDetail.contactButton')
                }}</a>
              </div>
              <div v-if="rankPdfUrlAvailable" class="list-pdf">
                <ul>
                  <li>
                    <a @click="handleRankPdf" class="cursor-pointer"
                      ><span class="txt">{{ t('productDetail.aboutRank') }}</span
                      ><span class="tab-pdf">PDF</span></a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!--ModalBid Start-->
    <BidConfirmModal
      v-model="bid.showBidConfirm"
      @refresh="refreshItem"
      :isAscendingAuction="isAscendingAuction"
    />
    <!--ModalBid End-->
  </div>
</template>

<style scoped>
/* Small devices (landscape phones, less than 768px) */
@media screen and (max-width: 767.98px) {
  input {
    font-size: 16px !important;
  }
}
@media screen and (min-width: 992px) {
  #main #item-data-no-image .item_main .bid-field .auction-type button {
    width: 100px;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl dt {
  width: 50%;
}
#main #item-data-no-image .item_main .bid-field .item-data dl dd {
  width: 50%;
}
</style>
