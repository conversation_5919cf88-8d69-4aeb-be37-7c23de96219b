<script setup>
import { computed, defineAsyncComponent, onBeforeMount, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { PATH_NAME } from '../../defined/const'

import useGetItemDetails from '../../composables/getItemDetails'

import { useLocale } from 'vuetify'
import TargetItem from '../../components/contact/TargetItem.vue'
import useApi from '../../composables/useApi'
import { useAuthStore } from '../../stores/auth'
import { useMemberStore } from '../../stores/member-info'
import { useMessageDialogStore } from '../../stores/messag-dialog'
import { useSearchResultStore } from '../../stores/search-results'

const PageTopLink = defineAsyncComponent(
  () => import(/* WebpackChunkName: "PageTopLink" */ '../../components/parts/PageTopLink.vue')
)

const props = defineProps({
  product: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      imgSrc: ''
    }
  }
})

const route = useRoute()
const router = useRouter()
const auth = useAuthStore()
const store = useSearchResultStore()
const { search } = useGetItemDetails()
const dialog = useMessageDialogStore()
const member = useMemberStore()
const { apiExcute, parseHtmlResponseError } = useApi()
const isConfirm = computed(() => route.path.includes('-confirm'))
const errMsg = ref({})
const showError = ref(false)
const { t: translate, current: currentLanguage } = useLocale()

const contact = reactive({
  companyName: null,
  memberName: '',
  email: '',
  emailConfirm: '',
  content: '',
  agreed: false
})
const contactErr = ref({
  memberName: false,
  email: false,
  emailConfirm: false,
  content: false,
  agreed: false
})

const resetError = () => {
  errMsg.value = {}
  Object.keys(contactErr.value).map((key) => (contactErr.value[key] = false))
}

// お問い合わせ処理
const requestContact = async (validate = true) => {
  const params = {
    languageCode: currentLanguage.value,
    validateFlag: validate,
    content: contact.content,
    inquiryData: {
      memberName: contact.memberName,
      email: contact.email,
      emailConfirm: contact.emailConfirm,
      companyName: contact.companyName
    }
  }

  if (!validate) {
    params.exhibitionItemNo = route.params.exhibitionItemNo || null
    params.exhibitionItemName = route.params.exhibitionItemNo
      ? store.productDetailsForContact?.productName
      : null
    params.itemNo = props.product?.itemNo ?? null
  }
  resetError()

  try {
    await apiExcute('public/request-inquiry', params)
      .then(() => {
        if (validate) {
          if (Object.values(contactErr.value).some((val) => val)) {
            console.log(Object.values(contactErr.value).map((val) => val))
          } else {
            router.push(
              route.params.exhibitionItemNo
                ? `${PATH_NAME.DETAIL}/${route.params.exhibitionItemNo}/contact-confirm`
                : PATH_NAME.CONTACT_CONFIRM
            )
          }
        } else {
          dialog.setShowMessage(translate('contact.inquiryDoneMessage'), {
            showOkButton: true,
            showCloseButton: false
          })
          showError.value = false
          watch(
            () => dialog.showMessageDialog,
            (newVal) => {
              if (!newVal && !showError.value) {
                router.push(PATH_NAME.TOP)
              }
            }
          )
        }
      })
      .catch((error) => {
        showError.value = true
        const err = parseHtmlResponseError(error)
        console.log({ err })
        Object.keys(err).map((key) => {
          contactErr.value[key] = true
          errMsg.value[key] = err[key]
        })
      })
  } catch (error) {
    console.error(error)
    showError.value = true
  } finally {
    window.scrollTo({ top: 200 })
  }
}

const getContactData = async () => {
  if (auth.isAuthenticated) {
    await apiExcute('private/get-change-info-constants')
      .then((res) => {
        member.setMemberInfo(res)
        contact.memberName = res.member.memberLastName + res.member.memberName
        contact.companyName = res.member.companyName
        contact.email = res.member.email
        contact.emailConfirm = ''
      })
      .catch((error) => {
        const err = parseHtmlResponseError(error)
        console.log({ err })
        dialog.setShowMessage(err, { isErr: true })
      })
  }
  if (route.params.exhibitionItemNo) {
    await search(route.params.exhibitionItemNo)
    store.setProductListForContact()
  }
}

// 会員情報取得して初期表示処理
onBeforeMount(async () => {
  await getContactData()
})

watch(
  () => currentLanguage.value,
  async () => {
    await getContactData()
  }
)
</script>

<template>
  <div class="contact-used">
    <PageTopLink />
    <section id="contact-form">
      <h1 class="mb0">{{ translate('guidance.contact') }}</h1>
      <div class="container">
        <form>
          <TargetItem
            v-if="route.params.exhibitionItemNo"
            :freeField="store.productDetailsForContact?.free_field"
          />
          <section id="entry-form">
            <div class="container">
              <p v-if="!isConfirm" class="entry-form-info">
                {{ translate('contact.entryFormInfo1')
                }}<em class="req">{{ translate('register.form.required') }}</em
                >{{ translate('contact.entryFormInfo2') }}
              </p>
              <p v-else class="comp-msg">
                {{ translate('contact.confirmMsg') }}
              </p>
              <table class="tbl-entry">
                <tbody class="item-inquiry-contact">
                  <tr>
                    <th :class="{ 'th-company-name': isConfirm }">
                      {{ translate('contact.companyName') }}
                      <em v-show="!isConfirm" class="req">{{
                        translate('register.form.required')
                      }}</em>
                    </th>
                    <td v-if="!isConfirm">
                      <input type="text" v-model="contact.companyName" class="iptW-M" required />
                      <p v-show="showError && contactErr.companyName" style="color: red">
                        {{ errMsg.companyName }}
                      </p>
                    </td>
                    <td v-else>
                      {{ contact.companyName }}
                    </td>
                  </tr>
                  <tr>
                    <th :class="{ 'th-member-name': isConfirm }">
                      {{ translate('contact.memberName')
                      }}<em v-show="!isConfirm" class="req">{{
                        translate('register.form.required')
                      }}</em>
                    </th>
                    <td v-if="!isConfirm">
                      <div>
                        <input type="text" v-model="contact.memberName" class="iptW-M" required />
                        <p v-show="showError && contactErr.memberName" style="color: red">
                          {{ errMsg.memberName }}
                        </p>
                      </div>
                    </td>
                    <td v-else>
                      {{ contact.memberName }}
                    </td>
                  </tr>
                  <tr>
                    <th :class="{ 'th-email': isConfirm }">
                      {{ translate('register.form.email')
                      }}<em v-show="!isConfirm" class="req">{{
                        translate('register.form.required')
                      }}</em>
                    </th>
                    <td v-if="!isConfirm">
                      <input v-model="contact.email" type="email" class="ime-dis iptW-M" required />
                      <p v-show="showError && contactErr.email" style="color: red">
                        {{ errMsg.email }}
                      </p>
                    </td>
                    <td v-else>
                      {{ contact.email }}
                    </td>
                  </tr>
                  <tr>
                    <th :class="{ 'th-email-confirm': isConfirm }">
                      {{ translate('register.form.email') }}<br class="only_pc" />{{
                        translate('contact.mailConfirm')
                      }}<em v-show="!isConfirm" class="req">{{
                        translate('register.form.required')
                      }}</em>
                    </th>
                    <td v-if="!isConfirm">
                      <input
                        v-model="contact.emailConfirm"
                        type="email"
                        class="ime-dis iptW-M"
                        required
                      />
                      <p v-show="showError && contactErr.emailConfirm" style="color: red">
                        {{ errMsg.emailConfirm }}
                      </p>
                    </td>
                    <td v-else>
                      {{ contact.emailConfirm }}
                    </td>
                  </tr>
                  <tr>
                    <th :class="{ 'th-inquiry': isConfirm }">
                      {{ translate('contact.inquiryContent')
                      }}<em v-show="!isConfirm" class="req">{{
                        translate('register.form.required')
                      }}</em>
                    </th>
                    <td v-if="!isConfirm">
                      <textarea v-model="contact.content"></textarea>
                      <p v-show="showError && contactErr.content" style="color: red">
                        {{ errMsg.content }}
                      </p>
                    </td>
                    <td v-else class="inquiry-content">
                      {{ contact.content }}
                    </td>
                  </tr>

                  <tr>
                    <th :class="{ 'th-inquiry': isConfirm }" class="ttl-agree">
                      {{ translate('contact.policyMessage1') }} <br class="only_pc" />{{
                        translate('contact.policyMessage2')
                      }}
                      <em v-show="!isConfirm" class="req">
                        {{ translate('register.form.required') }}
                      </em>
                    </th>
                    <td v-if="!isConfirm">
                      <div class="ipt-agree">
                        <label for="rule-chk">
                          <input
                            v-model="contact.agreed"
                            type="checkbox"
                            id="rule-chk"
                            class="checkbox-input"
                            required
                          />
                          <span class="checkbox-parts">{{ translate('common.agree') }}</span>
                        </label>
                      </div>
                    </td>
                    <td v-else>
                      <span>{{ translate('common.agree') }}</span>
                    </td>
                  </tr>
                  <tr v-show="!isConfirm" class="att-use">
                    <th>&nbsp;</th>
                    <td>
                      <p class="privacy-link">
                        {{ translate('register.form.privacyMessage1') }}
                        <RouterLink :to="PATH_NAME.PRIVACY" target="_blank">
                          {{ translate('register.form.privacyMessage2') }}
                        </RouterLink>
                        {{ translate('register.form.privacyMessage3') }}
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>
        </form>
      </div>
    </section>
    <div v-if="showError" class="comp-msg">
      <p style="color: red" class="comp-msg">
        {{ errMsg.message }}
      </p>
    </div>
    <div class="btn-form">
      <input
        v-if="!isConfirm"
        type="button"
        id="sbm-confirm"
        :value="translate('register.form.confirmButton')"
        :disabled="!contact.agreed"
        @click="requestContact(true)"
      />
      <input
        v-if="isConfirm"
        type="button"
        class="btn-back"
        :value="translate('common.back')"
        @click="$router.go(-1)"
      />
      <input
        v-if="isConfirm"
        type="button"
        id="sbm-confirm"
        :value="translate('common.send')"
        @click="requestContact(false)"
      />
    </div>
  </div>
</template>

<style scoped>
textarea,
input:not([type='button']) {
  background-color: white !important;
}

span.checkbox-parts::after {
  top: 55% !important;
  left: 7px !important;
}

p.comp-msg {
  margin: 0 0 60px;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  p.comp-msg {
    margin: 0 0 40px;
    text-align: left;
    font-size: 0.9rem;
  }
}

.th-inquiry {
  min-width: 310px !important;
}

@media screen and (max-width: 991px) {
  .th-inquiry {
    min-width: 230px !important;
  }
}

.inquiry-content {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
