<!DOCTYPE html>
<html>
<head>
    <title>Mock API Test</title>
</head>
<body>
    <h1>Mock API Test</h1>
    <p>Open browser console to see test results</p>
    
    <script>
        // Test if we're in development mode
        console.log('Environment check:');
        console.log('- Location:', window.location.href);
        console.log('- Is localhost:', window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
        
        // Test API endpoint
        const apiEndpoint = 'https://d13mmha1gm19pv.cloudfront.net/api/';
        console.log('- API Endpoint:', apiEndpoint);
        console.log('- Is production API:', apiEndpoint.includes('cloudfront.net'));
        
        // Test mock token
        const mockToken = 'mock-token-for-development';
        console.log('- Mock token:', mockToken);
        
        console.log('\nMock mode should be enabled in development when:');
        console.log('1. User has mock token, OR');
        console.log('2. API endpoint points to production CloudFront URL');
        console.log('\nThis should prevent CORS errors and infinite loops.');
    </script>
</body>
</html>
